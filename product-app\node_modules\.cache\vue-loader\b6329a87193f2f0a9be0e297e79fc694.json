{"remainingRequest": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\src\\views\\ImportantWork\\MajorProjects\\MajorProjectsDetailsCopy.vue?vue&type=template&id=d7bfb914", "dependencies": [{"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\src\\views\\ImportantWork\\MajorProjects\\MajorProjectsDetailsCopy.vue", "mtime": 1755080766649}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\vue-loader-v16\\dist\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQogIDxkaXYgY2xhc3M9Ik1ham9yUHJvamVjdHNEZXRhaWxzQ29weSI+DQogICAgTWFqb3JQcm9qZWN0c0RldGFpbHNDb3B5DQogIDwvZGl2Pg0K"}, {"version": 3, "sources": ["D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\src\\views\\ImportantWork\\MajorProjects\\MajorProjectsDetailsCopy.vue"], "names": [], "mappings": ";EACE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACzB,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "D:/zy/xm/h5/qdzx_h5/product-app/src/views/ImportantWork/MajorProjects/MajorProjectsDetailsCopy.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n  <div class=\"MajorProjectsDetailsCopy\">\r\n    MajorProjectsDetailsCopy\r\n  </div>\r\n</template>\r\n<script>\r\nimport { useRoute } from 'vue-router'\r\nimport { onMounted, reactive, toRefs } from 'vue'\r\nexport default {\r\n  name: 'MajorProjectsDetailsCopy',\r\n  components: {\r\n  },\r\n  setup () {\r\n    const route = useRoute()\r\n    // const $api = inject('$api')\r\n    const data = reactive({\r\n      title: route.query.title || '详情',\r\n      id: route.query.id,\r\n      details: {}\r\n    })\r\n    onMounted(() => {\r\n    })\r\n    return { ...toRefs(data) }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"less\">\r\n.MajorProjectsDetailsCopy {\r\n  width: 100%;\r\n  min-height: 100vh;\r\n  background: #fff;\r\n}\r\n</style>\r\n"]}]}