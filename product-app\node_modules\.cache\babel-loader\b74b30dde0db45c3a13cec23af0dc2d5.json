{"remainingRequest": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\babel-loader\\lib\\index.js!D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\vue-loader-v16\\dist\\templateLoader.js??ref--6!D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\src\\views\\ImportantWork\\attractInvestment\\attractInvestmentDetailsCopy.vue?vue&type=template&id=4b900e74", "dependencies": [{"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\src\\views\\ImportantWork\\attractInvestment\\attractInvestmentDetailsCopy.vue", "mtime": 1755080574904}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\babel.config.js", "mtime": 1731635626828}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\vue-loader-v16\\dist\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHsgb3BlbkJsb2NrIGFzIF9vcGVuQmxvY2ssIGNyZWF0ZUVsZW1lbnRCbG9jayBhcyBfY3JlYXRlRWxlbWVudEJsb2NrIH0gZnJvbSAidnVlIjsKY29uc3QgX2hvaXN0ZWRfMSA9IHsKICBjbGFzczogImF0dHJhY3RJbnZlc3RtZW50RGV0YWlsc0NvcHkiCn07CmV4cG9ydCBmdW5jdGlvbiByZW5kZXIoX2N0eCwgX2NhY2hlLCAkcHJvcHMsICRzZXR1cCwgJGRhdGEsICRvcHRpb25zKSB7CiAgcmV0dXJuIF9vcGVuQmxvY2soKSwgX2NyZWF0ZUVsZW1lbnRCbG9jaygiZGl2IiwgX2hvaXN0ZWRfMSwgIiAxMSAiKTsKfQ=="}, {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1"], "sources": ["D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\src\\views\\ImportantWork\\attractInvestment\\attractInvestmentDetailsCopy.vue"], "sourcesContent": ["<template>\r\n  <div class=\"attractInvestmentDetailsCopy\">\r\n    11\r\n  </div>\r\n</template>\r\n<script>\r\nimport { useRoute } from 'vue-router'\r\nimport { onMounted, reactive, toRefs } from 'vue'\r\nexport default {\r\n  name: 'attractInvestmentDetailsCopy',\r\n  components: {\r\n  },\r\n  setup () {\r\n    const route = useRoute()\r\n    // const $api = inject('$api')\r\n    const data = reactive({\r\n      title: route.query.title || '详情',\r\n      id: route.query.id,\r\n      details: {}\r\n    })\r\n    onMounted(() => {\r\n    })\r\n    return { ...toRefs(data) }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"less\">\r\n.attractInvestmentDetailsCopy {\r\n  width: 100%;\r\n  min-height: 100vh;\r\n  background: #fff;\r\n}\r\n</style>\r\n"], "mappings": ";;EACOA,KAAK,EAAC;AAA8B;;uBAAzCC,mBAAA,CAEM,OAFNC,UAEM,EAFoC,MAE1C", "ignoreList": []}]}