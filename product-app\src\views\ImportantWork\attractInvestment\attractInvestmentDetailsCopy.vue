<template>
  <div class="attractInvestmentDetailsCopy">
  </div>
</template>
<script>
import { useRoute } from 'vue-router'
import { inject, onMounted, reactive, toRefs } from 'vue'
export default {
  name: 'attractInvestmentDetailsCopy',
  components: {
  },
  setup () {
    const route = useRoute()
    const $api = inject('$api')
    const data = reactive({
      title: route.query.title || '详情',
      id: route.query.id,
      details: {}
    })
    onMounted(() => {
    })
    return { ...toRefs(data) }
  }
}
</script>
<style lang="less">
.attractInvestmentDetailsCopy {
  width: 100%;
  min-height: 100vh;
  background: #fff;
}
</style>
